import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/task_center_response_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/task_center_response_entity.g.dart';

@JsonSerializable()
class TaskCenterResponseEntity {
  List<TaskEntity> daily = [];
  List<TaskEntity> invite = [];
  List<TaskEntity> newUser = [];
  List<TaskEntity> trade = [];
  String signInRules = '';

  TaskCenterResponseEntity();

  factory TaskCenterResponseEntity.fromJson(Map<String, dynamic> json) => $TaskCenterResponseEntityFromJson(json);

  Map<String, dynamic> toJson() => $TaskCenterResponseEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TaskEntity {
  double amount = 0;
  int amountType = 0;
  int availableNum = 0;
  String content = '';
  String endTime = '';
  int expireType = 0;
  String icon = '';
  int id = 0;
  bool isCompleted = false;
  bool isReceived = false;
  String name = '';
  String rule = '';
  String startTime = '';
  bool status = false;
  int taskType = 0;
  int type = 0;

  TaskEntity();

  factory TaskEntity.fromJson(Map<String, dynamic> json) => $TaskEntityFromJson(json);

  Map<String, dynamic> toJson() => $TaskEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
