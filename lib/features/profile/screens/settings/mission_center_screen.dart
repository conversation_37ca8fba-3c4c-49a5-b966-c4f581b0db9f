import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gp_stock_app/core/models/apis/task.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/screens/settings/widgets/sign_in_streak_card.dart';
import 'package:gp_stock_app/features/profile/screens/settings/widgets/task_tab_bar.dart';
import 'package:gp_stock_app/features/profile/screens/settings/widgets/vip_card.dart';
import 'package:gp_stock_app/features/profile/screens/settings/widgets/vip_upgrade_card.dart';

import '../../logic/mission_center/cubit/mission_activity_cubit.dart';
import '../../logic/vip/vip_cubit.dart';

class MissionCenterScreen extends StatefulWidget {
  final bool isVip;
  const MissionCenterScreen({super.key, this.isVip = false});

  @override
  State<MissionCenterScreen> createState() => _MissionCenterScreenState();
}

class _MissionCenterScreenState extends State<MissionCenterScreen> {
  bool _initialLoadDone = false;

  Future<void> _refreshData(BuildContext context, {bool isManualRefresh = false}) async {
    if (!isManualRefresh && _initialLoadDone) {
      LogI('Skipping automatic refresh');
      return;
    }

    final futures = <Future>[];
    final missionCubit = context.read<MissionActivityCubit>();
    futures.add(missionCubit.getSignInLog());

    if (widget.isVip) {
      final vipCubit = context.read<VipCubit>();
      futures.add(vipCubit.getUserLevelConfig());
      futures.add(vipCubit.getNextUserLevel());
    }

    await Future.wait(futures);
    _initialLoadDone = true;

    missionCubit.getServerDate();
  }

  @override
  void initState() {
    super.initState();
    _refreshData(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        title: Text(widget.isVip ? 'vipCenter'.tr() : 'missionCenter'.tr()),
        actions: widget.isVip
            ? null
            : [
                TextButton(
                  onPressed: () => _showSignInRulesDialog(context),
                  child: Text(
                    'signInRules'.tr(),
                    style: context.textTheme.primary.w500,
                  ),
                ),
              ],
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: RefreshIndicator.adaptive(
            backgroundColor: context.theme.cardColor,
            onRefresh: () => _refreshData(context, isManualRefresh: true),
            child: widget.isVip
                ? const SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: Column(
                      spacing: 16,
                      children: [
                        VipCard(),
                        VipUpgradeCard(),
                      ],
                    ),
                  )
                : const SingleChildScrollView(
                    child: Column(
                      spacing: 16,
                      children: [
                        SignInStreakCard(),
                        TaskTabBar(),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  void _showSignInRulesDialog(BuildContext context) async {
    try {
      final taskData = await TaskApi.fetchTaskCenterData();
      if (taskData?.signInRules != null && taskData!.signInRules.isNotEmpty && context.mounted) {
        showDialog(
          context: context,
          builder: (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.gr),
            ),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.7,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Container(
                    padding: EdgeInsets.all(16.gw),
                    decoration: BoxDecoration(
                      color: context.theme.cardColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.gr),
                        topRight: Radius.circular(16.gr),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            'signInRules'.tr(),
                            style: context.textTheme.primary.fs18.w600,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Navigator.of(context).pop(),
                          child: Icon(
                            Icons.close,
                            color: context.colorTheme.textRegular,
                            size: 24.gw,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Divider(color: context.theme.dividerColor, height: 1),
                  // Content
                  Flexible(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(16.gw),
                      physics: const BouncingScrollPhysics(),
                      child: Html(
                        data: taskData.signInRules,
                        style: {
                          "p": Style(
                            color: context.colorTheme.textPrimary,
                            fontSize: FontSize(14.gsp),
                            fontFamily: 'PingFang SC',
                          ),
                          "strong": Style(
                            fontWeight: FontWeight.bold,
                          ),
                          "body": Style(
                            color: context.colorTheme.textPrimary,
                            fontSize: FontSize(14.gsp),
                          ),
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
    } catch (e) {
      LogE('Error fetching sign in rules: $e');
    }
  }
}
