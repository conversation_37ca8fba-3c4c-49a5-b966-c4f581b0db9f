part of 'activity_cubit.dart';

class ActivityState extends Equatable {
  final int selectedTab;
  final int selectedEventIndex;
  final DataStatus tasksFetchStatus;
  final DataStatus? rewardCollectionStatus;
  final List<TaskEntity>? newUserTasks;
  final List<TaskEntity>? tradeTasks;
  final List<TaskEntity>? dailyTasks;
  final String? signInRules;
  final String? error;

  const ActivityState({
    this.selectedTab = 0,
    this.selectedEventIndex = 0,
    this.tasksFetchStatus = DataStatus.idle,
    this.rewardCollectionStatus,
    this.newUserTasks,
    this.tradeTasks,
    this.dailyTasks,
    this.signInRules,
    this.error,
  });

  @override
  List<Object?> get props => [
        selectedTab,
        tasksFetchStatus,
        rewardCollectionStatus,
        newUserTasks,
        tradeTasks,
        dailyTasks,
        signInRules,
        error,
        selectedEventIndex
      ];

  ActivityState copyWith({
    int? selectedTab,
    int? selectedEventIndex,
    DataStatus? tasksFetchStatus,
    DataStatus? rewardCollectionStatus,
    List<TaskEntity>? newUserTasks,
    List<TaskEntity>? tradeTasks,
    List<TaskEntity>? dailyTasks,
    String? signInRules,
    String? error,
  }) {
    return ActivityState(
      selectedTab: selectedTab ?? this.selectedTab,
      selectedEventIndex: selectedEventIndex ?? this.selectedEventIndex,
      tasksFetchStatus: tasksFetchStatus ?? this.tasksFetchStatus,
      rewardCollectionStatus: rewardCollectionStatus ?? this.rewardCollectionStatus,
      newUserTasks: newUserTasks ?? this.newUserTasks,
      tradeTasks: tradeTasks ?? this.tradeTasks,
      dailyTasks: dailyTasks ?? this.dailyTasks,
      signInRules: signInRules ?? this.signInRules,
      error: error ?? this.error,
    );
  }
}
