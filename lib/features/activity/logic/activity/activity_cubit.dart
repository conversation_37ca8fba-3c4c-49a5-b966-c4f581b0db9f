import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/task.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/app/extension/helper.dart';
import '../../../../shared/constants/enums.dart';
import '../../domain/repository/activity_repository.dart';

part 'activity_state.dart';

@injectable
class ActivityCubit extends AuthAwareCubit<ActivityState> {
  final ActivityRepository _repository;

  ActivityCubit(this._repository) : super(const ActivityState());

  @override
  void onLoggedIn() => getTasks();

  @override
  void onLoggedOut() => emit(const ActivityState());

  void updateTab(int index) => emit(state.copyWith(selectedTab: index));

  void updateSelectedTab(int index) => emit(state.copyWith(selectedEventIndex: index));

  Future<void> getTasks() async {
    emit(state.copyWith(tasksFetchStatus: DataStatus.loading));
    TaskApi.fetchTaskCenterData();

    final res = await TaskApi.fetchTaskCenterData();
    if (res != null) {
      emit(state.copyWith(
        tasksFetchStatus: DataStatus.success,
        newUserTasks: res.newUser,
        tradeTasks: res.trade,
        dailyTasks: res.daily,
        error: null,
      ));
    } else {
      emit(state.copyWith(tasksFetchStatus: DataStatus.failed));
    }
  }

  Future<void> collectReward(TaskEntity task) async {
    if (task.isCompleted != true || task.isReceived == true) return;
    if (task.id == 0) return;

    emit(state.copyWith(rewardCollectionStatus: DataStatus.loading));
    final result = await _repository.collectReward(task.id);

    if (result.data != null) {
      Helper.showFlutterToast(
        'rewardCollected'.tr(),
      );
      await getTasks();
      emit(state.copyWith(rewardCollectionStatus: DataStatus.success));
    } else {
      emit(state.copyWith(rewardCollectionStatus: DataStatus.failed, error: result.error));
      Helper.showFlutterToast(
        result.error ?? 'Failed to collect reward',
      );
    }
  }

}
