import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config_entity.dart';

TradeHandlingFeeConfigList $TradeHandlingFeeConfigListFromJson(
    Map<String, dynamic> json) {
  final TradeHandlingFeeConfigList tradeHandlingFeeConfigList = TradeHandlingFeeConfigList();
  final List<TradeHandlingFeeConfigEntity>? list = (json['list'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<TradeHandlingFeeConfigEntity>(
          e) as TradeHandlingFeeConfigEntity).toList();
  if (list != null) {
    tradeHandlingFeeConfigList.list = list;
  }
  return tradeHandlingFeeConfigList;
}

Map<String, dynamic> $TradeHandlingFeeConfigListToJson(
    TradeHandlingFeeConfigList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension TradeHandlingFeeConfigListExtension on TradeHandlingFeeConfigList {
  TradeHandlingFeeConfigList copyWith({
    List<TradeHandlingFeeConfigEntity>? list,
  }) {
    return TradeHandlingFeeConfigList()
      ..list = list ?? this.list;
  }
}

TradeHandlingFeeConfigEntity $TradeHandlingFeeConfigEntityFromJson(
    Map<String, dynamic> json) {
  final TradeHandlingFeeConfigEntity tradeHandlingFeeConfigEntity = TradeHandlingFeeConfigEntity();
  final int? calculateType = jsonConvert.convert<int>(json['calculateType']);
  if (calculateType != null) {
    tradeHandlingFeeConfigEntity.calculateType = calculateType;
  }
  final double? calculateValue = jsonConvert.convert<double>(
      json['calculateValue']);
  if (calculateValue != null) {
    tradeHandlingFeeConfigEntity.calculateValue = calculateValue;
  }
  final int? chargePackageId = jsonConvert.convert<int>(
      json['chargePackageId']);
  if (chargePackageId != null) {
    tradeHandlingFeeConfigEntity.chargePackageId = chargePackageId;
  }
  final int? direction = jsonConvert.convert<int>(json['direction']);
  if (direction != null) {
    tradeHandlingFeeConfigEntity.direction = direction;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    tradeHandlingFeeConfigEntity.id = id;
  }
  final double? max = jsonConvert.convert<double>(json['max']);
  if (max != null) {
    tradeHandlingFeeConfigEntity.max = max;
  }
  final double? min = jsonConvert.convert<double>(json['min']);
  if (min != null) {
    tradeHandlingFeeConfigEntity.min = min;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    tradeHandlingFeeConfigEntity.name = name;
  }
  final int? rate = jsonConvert.convert<int>(json['rate']);
  if (rate != null) {
    tradeHandlingFeeConfigEntity.rate = rate;
  }
  final int? roundPrecision = jsonConvert.convert<int>(json['roundPrecision']);
  if (roundPrecision != null) {
    tradeHandlingFeeConfigEntity.roundPrecision = roundPrecision;
  }
  final int? roundType = jsonConvert.convert<int>(json['roundType']);
  if (roundType != null) {
    tradeHandlingFeeConfigEntity.roundType = roundType;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    tradeHandlingFeeConfigEntity.type = type;
  }
  return tradeHandlingFeeConfigEntity;
}

Map<String, dynamic> $TradeHandlingFeeConfigEntityToJson(
    TradeHandlingFeeConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['calculateType'] = entity.calculateType;
  data['calculateValue'] = entity.calculateValue;
  data['chargePackageId'] = entity.chargePackageId;
  data['direction'] = entity.direction;
  data['id'] = entity.id;
  data['max'] = entity.max;
  data['min'] = entity.min;
  data['name'] = entity.name;
  data['rate'] = entity.rate;
  data['roundPrecision'] = entity.roundPrecision;
  data['roundType'] = entity.roundType;
  data['type'] = entity.type;
  return data;
}

extension TradeHandlingFeeConfigEntityExtension on TradeHandlingFeeConfigEntity {
  TradeHandlingFeeConfigEntity copyWith({
    int? calculateType,
    double? calculateValue,
    int? chargePackageId,
    int? direction,
    int? id,
    double? max,
    double? min,
    String? name,
    int? rate,
    int? roundPrecision,
    int? roundType,
    int? type,
  }) {
    return TradeHandlingFeeConfigEntity()
      ..calculateType = calculateType ?? this.calculateType
      ..calculateValue = calculateValue ?? this.calculateValue
      ..chargePackageId = chargePackageId ?? this.chargePackageId
      ..direction = direction ?? this.direction
      ..id = id ?? this.id
      ..max = max ?? this.max
      ..min = min ?? this.min
      ..name = name ?? this.name
      ..rate = rate ?? this.rate
      ..roundPrecision = roundPrecision ?? this.roundPrecision
      ..roundType = roundType ?? this.roundType
      ..type = type ?? this.type;
  }
}