import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_trial_contract_config_entity.dart';

ApplyTrialContractConfigEntity $ApplyTrialContractConfigEntityFromJson(
    Map<String, dynamic> json) {
  final ApplyTrialContractConfigEntity applyTrialContractConfigEntity = ApplyTrialContractConfigEntity();
  final List<
      ApplyTrialContractConfigActivityRiskMap>? activityRiskMap = (json['activityRiskMap'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<ApplyTrialContractConfigActivityRiskMap>(
          e) as ApplyTrialContractConfigActivityRiskMap).toList();
  if (activityRiskMap != null) {
    applyTrialContractConfigEntity.activityRiskMap = activityRiskMap;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    applyTrialContractConfigEntity.currency = currency;
  }
  final double? interestCash = jsonConvert.convert<double>(
      json['interestCash']);
  if (interestCash != null) {
    applyTrialContractConfigEntity.interestCash = interestCash;
  }
  final double? useAmount = jsonConvert.convert<double>(json['useAmount']);
  if (useAmount != null) {
    applyTrialContractConfigEntity.useAmount = useAmount;
  }
  return applyTrialContractConfigEntity;
}

Map<String, dynamic> $ApplyTrialContractConfigEntityToJson(
    ApplyTrialContractConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['activityRiskMap'] =
      entity.activityRiskMap.map((v) => v.toJson()).toList();
  data['currency'] = entity.currency;
  data['interestCash'] = entity.interestCash;
  data['useAmount'] = entity.useAmount;
  return data;
}

extension ApplyTrialContractConfigEntityExtension on ApplyTrialContractConfigEntity {
  ApplyTrialContractConfigEntity copyWith({
    List<ApplyTrialContractConfigActivityRiskMap>? activityRiskMap,
    String? currency,
    double? interestCash,
    double? useAmount,
  }) {
    return ApplyTrialContractConfigEntity()
      ..activityRiskMap = activityRiskMap ?? this.activityRiskMap
      ..currency = currency ?? this.currency
      ..interestCash = interestCash ?? this.interestCash
      ..useAmount = useAmount ?? this.useAmount;
  }
}

ApplyTrialContractConfigActivityRiskMap $ApplyTrialContractConfigActivityRiskMapFromJson(
    Map<String, dynamic> json) {
  final ApplyTrialContractConfigActivityRiskMap applyTrialContractConfigActivityRiskMap = ApplyTrialContractConfigActivityRiskMap();
  final int? activityId = jsonConvert.convert<int>(json['activityId']);
  if (activityId != null) {
    applyTrialContractConfigActivityRiskMap.activityId = activityId;
  }
  final String? activityRule = jsonConvert.convert<String>(
      json['activityRule']);
  if (activityRule != null) {
    applyTrialContractConfigActivityRiskMap.activityRule = activityRule;
  }
  final List<double>? applyAmountList = (json['applyAmountList'] as List<
      dynamic>?)?.map(
          (e) => jsonConvert.convert<double>(e) as double).toList();
  if (applyAmountList != null) {
    applyTrialContractConfigActivityRiskMap.applyAmountList = applyAmountList;
  }
  final double? closeLossRadio = jsonConvert.convert<double>(
      json['closeLossRadio']);
  if (closeLossRadio != null) {
    applyTrialContractConfigActivityRiskMap.closeLossRadio = closeLossRadio;
  }
  final double? giveAmount = jsonConvert.convert<double>(json['giveAmount']);
  if (giveAmount != null) {
    applyTrialContractConfigActivityRiskMap.giveAmount = giveAmount;
  }
  final int? giveDay = jsonConvert.convert<int>(json['giveDay']);
  if (giveDay != null) {
    applyTrialContractConfigActivityRiskMap.giveDay = giveDay;
  }
  final double? giveRatio = jsonConvert.convert<double>(json['giveRatio']);
  if (giveRatio != null) {
    applyTrialContractConfigActivityRiskMap.giveRatio = giveRatio;
  }
  final double? interestRate = jsonConvert.convert<double>(
      json['interestRate']);
  if (interestRate != null) {
    applyTrialContractConfigActivityRiskMap.interestRate = interestRate;
  }
  final String? marketType = jsonConvert.convert<String>(json['marketType']);
  if (marketType != null) {
    applyTrialContractConfigActivityRiskMap.marketType = marketType;
  }
  final double? minApplyAmount = jsonConvert.convert<double>(
      json['minApplyAmount']);
  if (minApplyAmount != null) {
    applyTrialContractConfigActivityRiskMap.minApplyAmount = minApplyAmount;
  }
  final int? multiple = jsonConvert.convert<int>(json['multiple']);
  if (multiple != null) {
    applyTrialContractConfigActivityRiskMap.multiple = multiple;
  }
  final int? riskId = jsonConvert.convert<int>(json['riskId']);
  if (riskId != null) {
    applyTrialContractConfigActivityRiskMap.riskId = riskId;
  }
  final double? shareRatio = jsonConvert.convert<double>(json['shareRatio']);
  if (shareRatio != null) {
    applyTrialContractConfigActivityRiskMap.shareRatio = shareRatio;
  }
  final double? warnLossRadio = jsonConvert.convert<double>(
      json['warnLossRadio']);
  if (warnLossRadio != null) {
    applyTrialContractConfigActivityRiskMap.warnLossRadio = warnLossRadio;
  }
  return applyTrialContractConfigActivityRiskMap;
}

Map<String, dynamic> $ApplyTrialContractConfigActivityRiskMapToJson(
    ApplyTrialContractConfigActivityRiskMap entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['activityId'] = entity.activityId;
  data['activityRule'] = entity.activityRule;
  data['applyAmountList'] = entity.applyAmountList;
  data['closeLossRadio'] = entity.closeLossRadio;
  data['giveAmount'] = entity.giveAmount;
  data['giveDay'] = entity.giveDay;
  data['giveRatio'] = entity.giveRatio;
  data['interestRate'] = entity.interestRate;
  data['marketType'] = entity.marketType;
  data['minApplyAmount'] = entity.minApplyAmount;
  data['multiple'] = entity.multiple;
  data['riskId'] = entity.riskId;
  data['shareRatio'] = entity.shareRatio;
  data['warnLossRadio'] = entity.warnLossRadio;
  return data;
}

extension ApplyTrialContractConfigActivityRiskMapExtension on ApplyTrialContractConfigActivityRiskMap {
  ApplyTrialContractConfigActivityRiskMap copyWith({
    int? activityId,
    String? activityRule,
    List<double>? applyAmountList,
    double? closeLossRadio,
    double? giveAmount,
    int? giveDay,
    double? giveRatio,
    double? interestRate,
    String? marketType,
    double? minApplyAmount,
    int? multiple,
    int? riskId,
    double? shareRatio,
    double? warnLossRadio,
  }) {
    return ApplyTrialContractConfigActivityRiskMap()
      ..activityId = activityId ?? this.activityId
      ..activityRule = activityRule ?? this.activityRule
      ..applyAmountList = applyAmountList ?? this.applyAmountList
      ..closeLossRadio = closeLossRadio ?? this.closeLossRadio
      ..giveAmount = giveAmount ?? this.giveAmount
      ..giveDay = giveDay ?? this.giveDay
      ..giveRatio = giveRatio ?? this.giveRatio
      ..interestRate = interestRate ?? this.interestRate
      ..marketType = marketType ?? this.marketType
      ..minApplyAmount = minApplyAmount ?? this.minApplyAmount
      ..multiple = multiple ?? this.multiple
      ..riskId = riskId ?? this.riskId
      ..shareRatio = shareRatio ?? this.shareRatio
      ..warnLossRadio = warnLossRadio ?? this.warnLossRadio;
  }
}