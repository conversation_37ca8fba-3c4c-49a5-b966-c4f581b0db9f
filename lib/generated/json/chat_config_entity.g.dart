import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/chat/chat_config_entity.dart';

ChatConfigEntity $ChatConfigEntityFromJson(Map<String, dynamic> json) {
  final ChatConfigEntity chatConfigEntity = ChatConfigEntity();
  final String? imImage = jsonConvert.convert<String>(json['imImage']);
  if (imImage != null) {
    chatConfigEntity.imImage = imImage;
  }
  final String? nickName = jsonConvert.convert<String>(json['nickName']);
  if (nickName != null) {
    chatConfigEntity.nickName = nickName;
  }
  final String? sdkAppId = jsonConvert.convert<String>(json['sdkAppId']);
  if (sdkAppId != null) {
    chatConfigEntity.sdkAppId = sdkAppId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    chatConfigEntity.userId = userId;
  }
  final String? userSig = jsonConvert.convert<String>(json['userSig']);
  if (userSig != null) {
    chatConfigEntity.userSig = userSig;
  }
  return chatConfigEntity;
}

Map<String, dynamic> $ChatConfigEntityToJson(ChatConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['imImage'] = entity.imImage;
  data['nickName'] = entity.nickName;
  data['sdkAppId'] = entity.sdkAppId;
  data['userId'] = entity.userId;
  data['userSig'] = entity.userSig;
  return data;
}

extension ChatConfigEntityExtension on ChatConfigEntity {
  ChatConfigEntity copyWith({
    String? imImage,
    String? nickName,
    String? sdkAppId,
    String? userId,
    String? userSig,
  }) {
    return ChatConfigEntity()
      ..imImage = imImage ?? this.imImage
      ..nickName = nickName ?? this.nickName
      ..sdkAppId = sdkAppId ?? this.sdkAppId
      ..userId = userId ?? this.userId
      ..userSig = userSig ?? this.userSig;
  }
}

ChatServiceAccountConfig $ChatServiceAccountConfigFromJson(
    Map<String, dynamic> json) {
  final ChatServiceAccountConfig chatServiceAccountConfig = ChatServiceAccountConfig();
  final String? imAccount = jsonConvert.convert<String>(json['imAccount']);
  if (imAccount != null) {
    chatServiceAccountConfig.imAccount = imAccount;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    chatServiceAccountConfig.nickname = nickname;
  }
  final String? pImAccount = jsonConvert.convert<String>(json['pImAccount']);
  if (pImAccount != null) {
    chatServiceAccountConfig.pImAccount = pImAccount;
  }
  final String? pNickname = jsonConvert.convert<String>(json['pNickname']);
  if (pNickname != null) {
    chatServiceAccountConfig.pNickname = pNickname;
  }
  return chatServiceAccountConfig;
}

Map<String, dynamic> $ChatServiceAccountConfigToJson(
    ChatServiceAccountConfig entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['imAccount'] = entity.imAccount;
  data['nickname'] = entity.nickname;
  data['pImAccount'] = entity.pImAccount;
  data['pNickname'] = entity.pNickname;
  return data;
}

extension ChatServiceAccountConfigExtension on ChatServiceAccountConfig {
  ChatServiceAccountConfig copyWith({
    String? imAccount,
    String? nickname,
    String? pImAccount,
    String? pNickname,
  }) {
    return ChatServiceAccountConfig()
      ..imAccount = imAccount ?? this.imAccount
      ..nickname = nickname ?? this.nickname
      ..pImAccount = pImAccount ?? this.pImAccount
      ..pNickname = pNickname ?? this.pNickname;
  }
}